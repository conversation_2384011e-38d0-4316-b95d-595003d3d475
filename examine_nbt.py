#!/usr/bin/env python3
import nbtlib
import json

def examine_player_data():
    print("=== Examining Player Data ===")
    try:
        # Load the player data file
        player_file = nbtlib.load("playerdata/1bd0e62f-584f-4c8b-b5a1-d097ed12d059.dat")
        
        # Print the entire structure to understand what we have
        print("Player data structure:")
        print(json.dumps(player_file, indent=2, default=str))
        
        # Look specifically for inventory items
        if 'Inventory' in player_file:
            print("\n=== INVENTORY ITEMS ===")
            for i, item in enumerate(player_file['Inventory']):
                print(f"\nItem {i}:")
                print(json.dumps(item, indent=2, default=str))
                
                # Look for custom names or lore (where hidden messages might be)
                if 'tag' in item:
                    tag = item['tag']
                    if 'display' in tag:
                        display = tag['display']
                        if 'Name' in display:
                            print(f"  Custom Name: {display['Name']}")
                        if 'Lore' in display:
                            print(f"  Lore: {display['Lore']}")
                    
                    # Check for enchantments
                    if 'Enchantments' in tag:
                        print(f"  Enchantments: {tag['Enchantments']}")
        
    except Exception as e:
        print(f"Error reading player data: {e}")

def examine_level_data():
    print("\n=== Examining Level Data ===")
    try:
        level_file = nbtlib.load("level.dat")
        print("Level data structure:")
        print(json.dumps(level_file, indent=2, default=str))
    except Exception as e:
        print(f"Error reading level data: {e}")

if __name__ == "__main__":
    examine_player_data()
    examine_level_data()
