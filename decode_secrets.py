#!/usr/bin/env python3
import base64

def decode_base64(encoded_str):
    try:
        decoded = base64.b64decode(encoded_str).decode('utf-8')
        return decoded
    except Exception as e:
        print(f"Error decoding {encoded_str}: {e}")
        return None

# Found Base64 strings from the warrior's equipment
secrets = {
    "Diamond Sword (flag_part)": "QmxpdHp7TkJUXw==",
    "Enchanted Book (secret_code)": "Rm9yX0Jsb2Nrc30=", 
    "Diamond Chestplate (hidden_data)": "SXNfTm90X0p1c3Rf"
}

print("=== Decoding Hidden Messages ===")
decoded_parts = []

for item, encoded in secrets.items():
    decoded = decode_base64(encoded)
    if decoded:
        print(f"{item}: {encoded} -> {decoded}")
        decoded_parts.append(decoded)
    else:
        print(f"{item}: {encoded} -> FAILED TO DECODE")

print("\n=== Attempting to Reconstruct Flag ===")
print("Decoded parts:", decoded_parts)

# Try concatenating the parts
if len(decoded_parts) >= 3:
    # Try different orders
    possible_flags = [
        ''.join(decoded_parts),
        decoded_parts[0] + decoded_parts[2] + decoded_parts[1],
        decoded_parts[1] + decoded_parts[0] + decoded_parts[2],
        decoded_parts[1] + decoded_parts[2] + decoded_parts[0],
        decoded_parts[2] + decoded_parts[0] + decoded_parts[1],
        decoded_parts[2] + decoded_parts[1] + decoded_parts[0]
    ]
    
    print("\nPossible flag combinations:")
    for i, flag in enumerate(possible_flags):
        print(f"{i+1}: {flag}")
