#!/usr/bin/env python3

def analyze_string(s):
    print(f"Analyzing: {s}")
    print(f"Length: {len(s)}")
    print(f"Characters: {list(s)}")
    
    # Check if it looks like a flag format
    if s.startswith('Voubz[') and s.endswith(']'):
        print("Matches flag-like structure: Voubz[...] (should be Blitz{...})")
    
    return s

def keyboard_shift_analysis(s):
    """
    The hint about 'typed' under unusual conditions suggests keyboard-related encoding.
    Let's try different keyboard shift patterns.
    """
    
    # QWERTY keyboard layout
    qwerty_rows = [
        "1234567890",
        "qwertyuiop",
        "asdfghjkl",
        "zxcvbnm"
    ]
    
    # Create mapping for shifted keys (one position to the right)
    shift_map = {}
    
    # For each row, map each character to the one to its left
    for row in qwerty_rows:
        for i in range(len(row)):
            if i > 0:  # Can't shift the first character left
                shift_map[row[i]] = row[i-1]
    
    print("Keyboard shift mapping (right to left):")
    for k, v in shift_map.items():
        print(f"  {k} -> {v}")
    
    # Apply the shift
    result = ""
    for char in s:
        if char.lower() in shift_map:
            shifted = shift_map[char.lower()]
            # Preserve case
            if char.isupper():
                shifted = shifted.upper()
            result += shifted
        else:
            result += char  # Keep unchanged if not in mapping
    
    return result

def try_other_shifts(s):
    """Try other possible keyboard shifts and patterns"""

    # Try shifting left to right instead
    qwerty_rows = [
        "1234567890",
        "qwertyuiop",
        "asdfghjkl",
        "zxcvbnm"
    ]

    shift_map_lr = {}
    for row in qwerty_rows:
        for i in range(len(row)):
            if i < len(row) - 1:  # Can't shift the last character right
                shift_map_lr[row[i]] = row[i+1]

    result_lr = ""
    for char in s:
        if char.lower() in shift_map_lr:
            shifted = shift_map_lr[char.lower()]
            if char.isupper():
                shifted = shifted.upper()
            result_lr += shifted
        else:
            result_lr += char

    print(f"Left-to-right shift: {result_lr}")

    # Try Caesar cipher shifts
    print("\nTrying Caesar cipher shifts:")
    for shift in range(1, 26):
        result = ""
        for char in s:
            if char.isalpha():
                base = ord('A') if char.isupper() else ord('a')
                shifted = chr((ord(char) - base + shift) % 26 + base)
                result += shifted
            else:
                result += char
        if result.startswith('Blitz'):
            print(f"Caesar shift {shift}: {result}")

def try_hand_position_shifts(s):
    """
    Maybe the typing was done with hands in wrong position?
    Try common misalignments like hands shifted one key over
    """

    # Standard QWERTY layout
    keyboard_layout = {
        # Top row
        'q': 'w', 'w': 'e', 'e': 'r', 'r': 't', 't': 'y', 'y': 'u', 'u': 'i', 'i': 'o', 'o': 'p', 'p': 'q',
        # Middle row
        'a': 's', 's': 'd', 'd': 'f', 'f': 'g', 'g': 'h', 'h': 'j', 'j': 'k', 'k': 'l', 'l': 'a',
        # Bottom row
        'z': 'x', 'x': 'c', 'c': 'v', 'v': 'b', 'b': 'n', 'n': 'm', 'm': 'z'
    }

    # Try reverse mapping (what if they typed with hands shifted left?)
    reverse_map = {v: k for k, v in keyboard_layout.items()}

    result = ""
    for char in s:
        if char.lower() in reverse_map:
            shifted = reverse_map[char.lower()]
            if char.isupper():
                shifted = shifted.upper()
            result += shifted
        else:
            result += char

    print(f"Hand position shift (left): {result}")

    # Also try the original mapping
    result2 = ""
    for char in s:
        if char.lower() in keyboard_layout:
            shifted = keyboard_layout[char.lower()]
            if char.isupper():
                shifted = shifted.upper()
            result2 += shifted
        else:
            result2 += char

    print(f"Hand position shift (right): {result2}")

# Main analysis
suspicious_string = "Voubz[mabmy_lr_ut_jpf_mak_qdrwbj_euhs]"

print("=== CRYPTO ANALYSIS ===")
analyze_string(suspicious_string)

print("\n=== KEYBOARD SHIFT ANALYSIS ===")
shifted_result = keyboard_shift_analysis(suspicious_string)
print(f"\nResult after keyboard shift (right->left): {shifted_result}")

print("\n=== OTHER SHIFT ATTEMPTS ===")
try_other_shifts(suspicious_string)
